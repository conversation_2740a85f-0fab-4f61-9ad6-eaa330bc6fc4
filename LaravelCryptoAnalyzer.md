# 🚀 Laravel-Based Crypto Project Analyzer

This is a full-stack Laravel application that allows users to analyze crypto projects **before launch** by uploading a **smart contract address** and a **whitepaper** (PDF or URL). The app uses APIs like **GoPlus** for smart contract scanning and **OpenAI** for AI-driven whitepaper analysis. It also provides a **project score (10–100)** and a **price prediction** based on tokenomics.

---

## 🧩 Features

- ✅ Upload smart contract address & chain ID
- ✅ Upload whitepaper as PDF or link
- ✅ Analyze smart contract risks via GoPlus API
- ✅ Use OpenAI to summarize and evaluate whitepapers
- ✅ Predict 1-year token price based on supply and market cap
- ✅ Queue AI/API jobs with Laravel Horizon
- ✅ Display results on a clean dashboard

---

## 🧱 Tech Stack

| Layer     | Tech                         |
|-----------|------------------------------|
| Framework | Laravel 11                   |
| API Calls | Laravel HTTP Client          |
| Queues    | Redis + Laravel Horizon      |
| File Upload | Laravel Storage (local/S3) |
| PDF Parsing | `smalot/pdfparser` or `pdf-to-text` |
| AI        | OpenAI GPT-4 API             |
| Contract Scanner | GoPlus API            |
| Frontend  | Blade or Inertia.js (Vue)    |
| DB        | MySQL or PostgreSQL          |

---

## 🔧 Setup Instructions

### 1. Clone the Repo & Install Laravel

```bash
composer create-project laravel/laravel crypto-analyzer
cd crypto-analyzer
```

### 2. Install Required Packages

```bash
composer require guzzlehttp/guzzle
composer require smalot/pdfparser
composer require predis/predis
```

### 3. Configure `.env`

```env
APP_NAME=CryptoAnalyzer
APP_URL=http://localhost:8000

OPENAI_API_KEY=your_openai_key
GOPLUS_API_KEY=your_goplus_api_key
GOPLUS_API_SECRET=your_goplus_api_secret
```

### 4. Setup Redis for Queues

Install Redis server locally or use a managed service.

```bash
composer require laravel/horizon
php artisan horizon:install
php artisan migrate
```

Start queue:
```bash
php artisan horizon
```

---

## 📤 AI-Only Analysis Flow

1. User uploads `.pdf` or submits a whitepaper URL.
2. **PDF files are sent directly to OpenAI** (not stored locally).
3. **OpenAI processes the entire document** using GPT-4o vision capabilities.
4. A Job is queued to handle the OpenAI file upload and analysis.
5. Score, summary, and findings are saved to DB.
6. Results are shown on a project detail page.

### 🔒 Privacy & Security
- **No local storage**: PDF files are sent directly to OpenAI and not stored on your server.
- **OpenAI processing**: All document analysis is performed by OpenAI's AI models.
- **Privacy compliance**: Users are notified that files are sent to OpenAI for processing.

---

## 📡 API Integrations

### 🛡️ GoPlus Smart Contract Analysis

**Endpoint:**
```
GET https://api.gopluslabs.io/api/v1/token_security/{chain_id}?contract_addresses={address}
```

**Laravel Example:**
```php
$response = Http::withHeaders([
  'API-KEY' => env('GOPLUS_API_KEY')
])->get($url);
```

---

### 🧠 OpenAI Whitepaper Analysis (AI-Only)

**New Direct File Upload Approach:**
```php
// Upload PDF directly to OpenAI
$analysis = $openAIService->analyzeWhitepaperFromFile($filePath, $fileName);

// Or analyze from URL
$analysis = $openAIService->analyzeWhitepaperFromUrl($url);
```

**Key Features:**
- Uses GPT-4o vision capabilities for comprehensive document analysis
- Processes charts, diagrams, and technical specifications
- No local text extraction required
- Enhanced error handling for API limits and file format issues

**Laravel Request:**
```php
$response = Http::withToken(env('OPENAI_API_KEY'))->post('https://api.openai.com/v1/chat/completions', [
  'model' => 'gpt-4',
  'messages' => [
    ['role' => 'system', 'content' => 'You are a crypto analyst.'],
    ['role' => 'user', 'content' => $prompt]
  ]
]);
```

---

## 🧠 Scoring Logic

Weights (example):
- Security (GoPlus) = 40%
- Whitepaper Quality = 30%
- Tokenomics Soundness = 20%
- Transparency = 10%

Combine each into a final score (10–100).

---

## 💰 Token Price Predictor

**Formula:**
```php
$price = $market_cap / $total_supply;
```

Add assumption notes and round to 6–8 decimals.

---

## 📁 Suggested Folder Structure

```
app/
  Http/Controllers/AnalysisController.php
  Jobs/AnalyzeWhitepaperJob.php
  Services/OpenAIService.php
  Services/GoPlusService.php

resources/views/
  upload.blade.php
  result.blade.php
```

---

## ✅ Deployment Tips

- Use **Laravel Forge** + Redis for scaling
- Or go serverless with **Laravel Vapor**
- Cache and queue AI jobs for responsiveness
- Optimize PDF parsing by limiting to first few pages

---

## 🧪 Future Features

- Team wallet analysis
- Token unlock schedules
- Social sentiment (Twitter, Discord)
- Launchpad risk rating

---

Built with ❤️ using Laravel.
