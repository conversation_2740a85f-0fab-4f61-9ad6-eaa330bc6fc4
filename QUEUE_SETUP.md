# Queue Worker Setup Guide

This guide covers setting up and managing Laravel queue workers for the Crypto Analyzer application.

## 🚀 Quick Start

### Development Environment

1. **Start workers locally:**
   ```bash
   php artisan queue:supervisor start --workers=2
   ```

2. **Check status:**
   ```bash
   php artisan queue:supervisor status
   ```

3. **Access web dashboard:**
   - Go to your application dashboard
   - View "Queue Workers" section for real-time status

## 🏭 Production Setup

### Linux/Ubuntu with Supervisor

1. **Install Supervisor:**
   ```bash
   sudo apt-get update
   sudo apt-get install supervisor
   ```

2. **Setup configuration:**
   ```bash
   # Update paths in deploy script
   nano scripts/deploy.sh
   # Change PROJECT_PATH and USER variables
   
   # Run setup
   chmod +x scripts/deploy.sh
   ./scripts/deploy.sh setup
   ```

3. **Deploy and start:**
   ```bash
   ./scripts/deploy.sh deploy
   ```

### Windows Production

1. **Basic setup:**
   ```batch
   # Edit paths in the script
   notepad scripts\windows-deploy.bat
   # Update PROJECT_PATH and PHP_PATH
   
   # Start workers
   scripts\windows-deploy.bat start
   ```

2. **Windows Service (Recommended):**
   ```batch
   # Download and install NSSM from https://nssm.cc/download
   # Add NSSM to your PATH
   
   # Install as service
   scripts\windows-deploy.bat install-service
   
   # Start service
   sc start LaravelQueueWorker
   ```

## 🔧 Configuration

### Environment Variables

Add these to your `.env` file:

```env
# Queue Configuration
QUEUE_CONNECTION=database
DB_QUEUE_TABLE=jobs
DB_QUEUE=default
DB_QUEUE_RETRY_AFTER=90

# For Redis (optional)
# QUEUE_CONNECTION=redis
# REDIS_QUEUE_CONNECTION=default
# REDIS_QUEUE=default
```

### Worker Configuration

Edit supervisor configurations in the `supervisor/` directory:

- `laravel-worker.conf` - Standard supervisor config
- `laravel-horizon.conf` - For Redis-based queues with Horizon

## 📊 Monitoring

### Web Dashboard

The application dashboard provides:
- Real-time worker status
- Worker count and uptime
- Start/stop/restart controls
- Log file access
- Queue health indicators

### Command Line Monitoring

```bash
# Check worker status
php artisan queue:supervisor status

# View queue statistics
php artisan queue:work --help

# Monitor logs
tail -f storage/logs/worker*.log
```

### API Endpoints

- `GET /queue/status` - Get worker status
- `POST /queue/start` - Start workers
- `POST /queue/stop` - Stop workers
- `POST /queue/restart` - Restart workers

## 🚨 Troubleshooting

### Common Issues

1. **Workers not starting:**
   ```bash
   # Check PHP path
   which php
   
   # Check permissions
   ls -la storage/logs/
   
   # Check database connection
   php artisan tinker
   >>> DB::connection()->getPdo();
   ```

2. **Workers stopping unexpectedly:**
   ```bash
   # Check logs
   cat storage/logs/worker*.log
   
   # Check system resources
   free -h
   ps aux | grep queue:work
   ```

3. **Jobs not processing:**
   ```bash
   # Check queue table
   php artisan tinker
   >>> DB::table('jobs')->count();
   
   # Check failed jobs
   php artisan queue:failed
   ```

### Windows-Specific Issues

1. **Path issues:**
   - Ensure PHP path is correct in scripts
   - Use full paths, not relative paths
   - Check for spaces in paths (use quotes)

2. **Service issues:**
   ```batch
   # Check service status
   sc query LaravelQueueWorker
   
   # View service logs
   # Check Windows Event Viewer > Applications
   ```

## 🔄 Deployment Workflow

### Standard Deployment

```bash
# 1. Pull latest code
git pull origin main

# 2. Update dependencies
composer install --no-dev --optimize-autoloader

# 3. Run migrations
php artisan migrate --force

# 4. Clear caches
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 5. Restart workers
php artisan queue:supervisor restart --workers=3
```

### Automated Deployment

Use the provided deployment scripts:

```bash
# Linux
./scripts/deploy.sh deploy

# Windows
scripts\windows-deploy.bat deploy
```

## 📈 Performance Tuning

### Worker Count

- **Development:** 1-2 workers
- **Small production:** 2-4 workers
- **Large production:** 4-8 workers

### Memory Management

```bash
# Set memory limits in supervisor config
command=php artisan queue:work --memory=512 --timeout=300

# Monitor memory usage
ps aux | grep queue:work
```

### Database Optimization

```sql
-- Add indexes for better performance
CREATE INDEX idx_jobs_queue_reserved_at ON jobs(queue, reserved_at);
CREATE INDEX idx_failed_jobs_failed_at ON failed_jobs(failed_at);
```

## 🔐 Security

### File Permissions

```bash
# Set proper permissions
chown -R www-data:www-data storage/
chmod -R 755 storage/
chmod -R 644 storage/logs/
```

### Process Isolation

- Run workers as dedicated user (www-data)
- Limit memory and CPU usage
- Use process monitoring tools

## 📚 Additional Resources

- [Laravel Queue Documentation](https://laravel.com/docs/queues)
- [Supervisor Documentation](http://supervisord.org/)
- [NSSM Documentation](https://nssm.cc/usage)
- [Laravel Horizon](https://laravel.com/docs/horizon) (for Redis queues)
