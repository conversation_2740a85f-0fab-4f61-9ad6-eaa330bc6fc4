<?php

namespace App\Console\Commands;

use App\Services\QueueSupervisorService;
use Illuminate\Console\Command;

class QueueSupervisorCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'queue:supervisor 
                            {action : The action to perform (start|stop|restart|status)}
                            {--workers=2 : Number of workers to start}';

    /**
     * The console command description.
     */
    protected $description = 'Manage Laravel queue workers with supervisor functionality';

    private QueueSupervisorService $supervisorService;

    public function __construct(QueueSupervisorService $supervisorService)
    {
        parent::__construct();
        $this->supervisorService = $supervisorService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $action = $this->argument('action');
        $workerCount = (int) $this->option('workers');

        switch ($action) {
            case 'start':
                return $this->startWorkers($workerCount);
            
            case 'stop':
                return $this->stopWorkers();
            
            case 'restart':
                return $this->restartWorkers($workerCount);
            
            case 'status':
                return $this->showStatus();
            
            default:
                $this->error("Invalid action: {$action}");
                $this->info('Available actions: start, stop, restart, status');
                return 1;
        }
    }

    /**
     * Start queue workers.
     */
    private function startWorkers(int $workerCount): int
    {
        if ($this->supervisorService->areWorkersRunning()) {
            $this->warn('Queue workers are already running. Use "restart" to restart them.');
            return 0;
        }

        $this->info("Starting {$workerCount} queue workers...");
        
        $results = $this->supervisorService->startWorkers($workerCount);
        
        foreach ($results as $result) {
            if ($result['status'] === 'started') {
                $this->info("✓ Worker {$result['worker_id']} started successfully");
            } else {
                $this->error("✗ Worker {$result['worker_id']} failed to start: {$result['error']}");
            }
        }

        $this->info('Queue workers started. Use "php artisan queue:supervisor status" to check status.');
        return 0;
    }

    /**
     * Stop queue workers.
     */
    private function stopWorkers(): int
    {
        if (!$this->supervisorService->areWorkersRunning()) {
            $this->warn('No queue workers are currently running.');
            return 0;
        }

        $this->info('Stopping queue workers...');
        
        if ($this->supervisorService->stopWorkers()) {
            $this->info('✓ Queue workers stopped successfully');
            return 0;
        } else {
            $this->error('✗ Failed to stop queue workers');
            return 1;
        }
    }

    /**
     * Restart queue workers.
     */
    private function restartWorkers(int $workerCount): int
    {
        $this->info("Restarting queue workers with {$workerCount} workers...");
        
        $results = $this->supervisorService->restartWorkers($workerCount);
        
        foreach ($results as $result) {
            if ($result['status'] === 'started') {
                $this->info("✓ Worker {$result['worker_id']} restarted successfully");
            } else {
                $this->error("✗ Worker {$result['worker_id']} failed to restart: {$result['error']}");
            }
        }

        return 0;
    }

    /**
     * Show worker status.
     */
    private function showStatus(): int
    {
        $status = $this->supervisorService->getWorkerStatus();

        $this->info('Queue Worker Status:');
        $this->table(
            ['Property', 'Value'],
            [
                ['Running', $status['running'] ? '✓ Yes' : '✗ No'],
                ['Worker Count', $status['count']],
                ['Started At', $status['started_at'] ?? 'N/A'],
                ['Uptime', $status['uptime'] ?? 'N/A'],
            ]
        );

        if (!empty($status['log_files'])) {
            $this->info('Log Files:');
            $logData = [];
            foreach ($status['log_files'] as $log) {
                $logData[] = [
                    $log['name'],
                    $this->formatBytes($log['size']),
                    date('Y-m-d H:i:s', $log['modified'])
                ];
            }
            $this->table(['File', 'Size', 'Modified'], $logData);
        }

        return 0;
    }

    /**
     * Format bytes to human readable format.
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
