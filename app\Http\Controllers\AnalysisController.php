<?php

namespace App\Http\Controllers;

use App\Jobs\AnalyzeSecurityJob;
use App\Jobs\AnalyzeWhitepaperJob;
use App\Jobs\PredictPriceJob;
use App\Models\AnalysisJob;
use App\Models\Project;
// use App\Services\PdfParserService; // No longer needed for whitepaper analysis
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class AnalysisController extends Controller
{
    // private PdfParserService $pdfParserService; // No longer needed for whitepaper analysis

    // Constructor no longer needs PdfParserService for whitepaper analysis
    // public function __construct(PdfParserService $pdfParserService)
    // {
    //     $this->pdfParserService = $pdfParserService;
    // }

    /**
     * Show the upload form.
     */
    public function index()
    {
        try {
            $recentProjects = Project::latest()
                ->take(5)
                ->get();
        } catch (\Exception $e) {
            // Handle case where database tables don't exist yet
            $recentProjects = collect();
        }

        return view('analysis.index', compact('recentProjects'));
    }

    /**
     * Handle project creation and analysis initiation.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'contract_address' => 'required|string|min:1|max:255',
            'chain_id' => 'required|string|in:1,56,42161,137,324,59144,8453,534352,10,43114,250,25,66,128,100,10001,tron,321,201022,5000,204,42766,81457,169,80094,2741,177,146,1514',
            'project_name' => 'nullable|string|max:255',
            'whitepaper_type' => 'required|in:pdf,url',
            'whitepaper_file' => 'required_if:whitepaper_type,pdf|file|mimes:pdf|max:51200', // 50MB max
            'whitepaper_url' => 'required_if:whitepaper_type,url|nullable|url|max:500',
        ]);

        if ($validator->fails()) {
            return back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Check if project already exists
            $existingProject = Project::where('contract_address', $request->contract_address)
                ->where('chain_id', $request->chain_id)
                ->first();

            if ($existingProject) {
                return redirect()
                    ->route('analysis.show', $existingProject)
                    ->with('info', 'This project has already been analyzed.');
            }

            // Create new project
            $project = $this->createProject($request);

            // Handle whitepaper upload/processing
            $this->handleWhitepaper($project, $request);

            // Create analysis jobs
            $this->createAnalysisJobs($project);

            // Update project status
            $project->update(['status' => 'analyzing']);

            return redirect()
                ->route('analysis.show', $project)
                ->with('success', 'Project analysis has been started. Results will be available shortly.');

        } catch (\Exception $e) {
            return back()
                ->withErrors(['error' => 'Failed to start analysis: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Show project analysis results.
     */
    public function show(Project $project)
    {
        $project->load(['analysisJobs', 'scoringComponents']);

        return view('analysis.show', compact('project'));
    }

    /**
     * Get project analysis status (AJAX endpoint).
     */
    public function status(Project $project)
    {
        $project->load(['analysisJobs', 'scoringComponents']);

        return response()->json([
            'status' => $project->status,
            'overall_score' => $project->overall_score,
            'progress' => $this->calculateProgress($project),
            'jobs' => $project->analysisJobs->map(function ($job) {
                return [
                    'type' => $job->job_type,
                    'status' => $job->status,
                    'duration' => $job->getFormattedDuration(),
                    'error' => $job->error_message
                ];
            }),
            'scoring_components' => $project->scoringComponents->map(function ($component) {
                return [
                    'type' => $component->component_type,
                    'score' => $component->score,
                    'weight' => $component->weight,
                    'grade' => $component->getGrade(),
                    'color' => $component->getScoreColor()
                ];
            })
        ]);
    }

    /**
     * List all projects.
     */
    public function list()
    {
        $projects = Project::with(['scoringComponents'])
            ->latest()
            ->paginate(20);

        return view('analysis.list', compact('projects'));
    }

    /**
     * Delete a project.
     */
    public function destroy(Project $project)
    {
        try {
            // Delete associated files
            if ($project->whitepaper_path && Storage::exists($project->whitepaper_path)) {
                Storage::delete($project->whitepaper_path);
            }

            // Delete project (cascade will handle related records)
            $project->delete();

            return redirect()
                ->route('analysis.list')
                ->with('success', 'Project deleted successfully.');

        } catch (\Exception $e) {
            return back()
                ->withErrors(['error' => 'Failed to delete project: ' . $e->getMessage()]);
        }
    }

    /**
     * Create a new project record.
     */
    private function createProject(Request $request): Project
    {
        return Project::create([
            'name' => $request->project_name ?: 'Unknown Project',
            'contract_address' => $request->contract_address,
            'chain_id' => $request->chain_id,
            'whitepaper_type' => $request->whitepaper_type,
            'status' => 'pending'
        ]);
    }

    /**
     * Handle whitepaper upload or URL processing.
     * Note: PDFs are no longer stored locally - they will be uploaded directly to OpenAI.
     */
    private function handleWhitepaper(Project $project, Request $request): void
    {
        if ($request->whitepaper_type === 'pdf' && $request->hasFile('whitepaper_file')) {
            // For PDF uploads, we'll store minimal info and let the job handle OpenAI upload
            $file = $request->file('whitepaper_file');

            // Basic validation (OpenAI will handle detailed PDF validation)
            if ($file->getSize() > 50 * 1024 * 1024) { // 50MB limit
                throw new \Exception('PDF file is too large. Maximum size is 50MB.');
            }

            if ($file->getMimeType() !== 'application/pdf') {
                throw new \Exception('Invalid file type. Only PDF files are allowed.');
            }

            // Store the original filename for reference
            $project->update([
                'whitepaper_path' => $file->getClientOriginalName() // Store original name for reference
            ]);

        } elseif ($request->whitepaper_type === 'url') {
            // Handle URL - this will be sent directly to OpenAI
            $project->update([
                'whitepaper_url' => $request->whitepaper_url
            ]);
        }
    }

    /**
     * Create analysis jobs for the project.
     */
    private function createAnalysisJobs(Project $project): void
    {
        // Create security analysis job
        $securityJob = AnalysisJob::create([
            'project_id' => $project->id,
            'job_type' => 'security_scan',
            'status' => 'pending',
            'job_data' => [
                'contract_address' => $project->contract_address,
                'chain_id' => $project->chain_id
            ]
        ]);

        // Create whitepaper analysis job
        $jobData = [
            'whitepaper_type' => $project->whitepaper_type,
            'whitepaper_path' => $project->whitepaper_path,
            'whitepaper_url' => $project->whitepaper_url
        ];

        // If it's a PDF upload, store the file temporarily for the job
        if ($request->whitepaper_type === 'pdf' && $request->hasFile('whitepaper_file')) {
            $file = $request->file('whitepaper_file');
            $tempPath = $file->store('temp_whitepapers', 'local');
            $jobData['temp_file_path'] = $tempPath;
            $jobData['original_filename'] = $file->getClientOriginalName();
        }

        $whitepaperJob = AnalysisJob::create([
            'project_id' => $project->id,
            'job_type' => 'whitepaper_analysis',
            'status' => 'pending',
            'job_data' => $jobData
        ]);

        // Create price prediction job
        $priceJob = AnalysisJob::create([
            'project_id' => $project->id,
            'job_type' => 'price_prediction',
            'status' => 'pending',
            'job_data' => []
        ]);

        // Dispatch jobs to queue
        AnalyzeSecurityJob::dispatch($project, $securityJob);
        AnalyzeWhitepaperJob::dispatch($project, $whitepaperJob);
        PredictPriceJob::dispatch($project, $priceJob);
    }

    /**
     * Calculate analysis progress percentage.
     */
    private function calculateProgress(Project $project): int
    {
        $totalJobs = $project->analysisJobs->count();
        
        if ($totalJobs === 0) {
            return 0;
        }

        $completedJobs = $project->analysisJobs->where('status', 'completed')->count();
        $processingJobs = $project->analysisJobs->where('status', 'processing')->count();

        // Completed jobs count as 100%, processing jobs as 50%
        $progress = (($completedJobs * 100) + ($processingJobs * 50)) / $totalJobs;

        return min(100, max(0, round($progress)));
    }
}
