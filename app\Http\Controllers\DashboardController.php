<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Models\AnalysisJob;
use App\Services\QueueSupervisorService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    private QueueSupervisorService $queueSupervisor;

    public function __construct(QueueSupervisorService $queueSupervisor = null)
    {
        $this->queueSupervisor = $queueSupervisor ?: new QueueSupervisorService();
    }

    /**
     * Show the dashboard.
     */
    public function index()
    {
        try {
            $stats = $this->getDashboardStats();
            $recentProjects = $this->getRecentProjects();
            $topProjects = $this->getTopScoredProjects();
            $chartData = $this->getChartData();
            try {
                $queueStatus = $this->queueSupervisor->getWorkerStatus();
            } catch (\Exception $queueError) {
                \Log::warning('Failed to get queue status for dashboard', ['error' => $queueError->getMessage()]);
                $queueStatus = ['running' => false, 'count' => 0, 'started_at' => null, 'uptime' => null, 'log_files' => []];
            }
        } catch (\Exception $e) {
            // Handle case where database tables don't exist yet
            $stats = [
                'total_projects' => 0,
                'completed_analyses' => 0,
                'pending_analyses' => 0,
                'failed_analyses' => 0,
                'average_score' => 0,
                'high_risk_projects' => 0,
                'low_risk_projects' => 0,
                'total_jobs_processed' => 0,
            ];
            $recentProjects = collect();
            $topProjects = collect();
            $chartData = [
                'score_distribution' => [],
                'chain_distribution' => [],
                'risk_levels' => [],
                'analysis_timeline' => [],
            ];
            $queueStatus = ['running' => false, 'count' => 0, 'started_at' => null, 'uptime' => null, 'log_files' => []];
        }

        return view('dashboard.index', compact('stats', 'recentProjects', 'topProjects', 'chartData', 'queueStatus'));
    }

    /**
     * Start queue workers.
     */
    public function startWorkers(Request $request)
    {
        try {
            $workerCount = (int) $request->input('workers', 2);

            // Validate worker count
            if ($workerCount < 1 || $workerCount > 8) {
                return response()->json([
                    'success' => false,
                    'message' => 'Worker count must be between 1 and 8'
                ], 400);
            }

            $results = $this->queueSupervisor->startWorkers($workerCount);

            return response()->json([
                'success' => true,
                'message' => "Started {$workerCount} queue workers successfully",
                'results' => $results
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to start workers', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to start workers: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Stop queue workers.
     */
    public function stopWorkers()
    {
        try {
            $success = $this->queueSupervisor->stopWorkers();

            return response()->json([
                'success' => $success,
                'message' => $success ? 'Queue workers stopped successfully' : 'Failed to stop workers'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to stop workers: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Restart queue workers.
     */
    public function restartWorkers(Request $request)
    {
        $workerCount = $request->input('workers', 2);

        try {
            $results = $this->queueSupervisor->restartWorkers($workerCount);

            return response()->json([
                'success' => true,
                'message' => "Restarted {$workerCount} queue workers successfully",
                'results' => $results
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to restart workers: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get queue worker status.
     */
    public function getWorkerStatus()
    {
        try {
            $status = $this->queueSupervisor->getWorkerStatus();

            return response()->json([
                'success' => true,
                'status' => $status
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to get worker status', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get worker status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test endpoint to verify queue management is working.
     */
    public function testQueue()
    {
        try {
            return response()->json([
                'success' => true,
                'message' => 'Queue management endpoint is working',
                'timestamp' => now()->toISOString(),
                'php_version' => PHP_VERSION,
                'os' => PHP_OS_FAMILY
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Test failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get dashboard statistics.
     */
    private function getDashboardStats(): array
    {
        return [
            'total_projects' => Project::count(),
            'completed_analyses' => Project::where('status', 'completed')->count(),
            'pending_analyses' => Project::where('status', 'analyzing')->count(),
            'failed_analyses' => Project::where('status', 'failed')->count(),
            'average_score' => Project::where('status', 'completed')
                ->whereNotNull('overall_score')
                ->avg('overall_score'),
            'high_risk_projects' => Project::where('status', 'completed')
                ->where('overall_score', '<', 40)
                ->count(),
            'low_risk_projects' => Project::where('status', 'completed')
                ->where('overall_score', '>=', 80)
                ->count(),
            'total_jobs_processed' => AnalysisJob::where('status', 'completed')->count(),
        ];
    }

    /**
     * Get recent projects.
     */
    private function getRecentProjects()
    {
        return Project::with(['scoringComponents'])
            ->latest()
            ->take(10)
            ->get();
    }

    /**
     * Get top scored projects.
     */
    private function getTopScoredProjects()
    {
        return Project::with(['scoringComponents'])
            ->where('status', 'completed')
            ->whereNotNull('overall_score')
            ->orderBy('overall_score', 'desc')
            ->take(10)
            ->get();
    }

    /**
     * Get chart data for dashboard visualizations.
     */
    private function getChartData(): array
    {
        return [
            'score_distribution' => $this->getScoreDistribution(),
            'chain_distribution' => $this->getChainDistribution(),
            'risk_levels' => $this->getRiskLevels(),
            'analysis_timeline' => $this->getAnalysisTimeline(),
        ];
    }

    /**
     * Get score distribution data.
     */
    private function getScoreDistribution(): array
    {
        $distribution = Project::where('status', 'completed')
            ->whereNotNull('overall_score')
            ->selectRaw('
                CASE 
                    WHEN overall_score >= 90 THEN "90-100"
                    WHEN overall_score >= 80 THEN "80-89"
                    WHEN overall_score >= 70 THEN "70-79"
                    WHEN overall_score >= 60 THEN "60-69"
                    WHEN overall_score >= 50 THEN "50-59"
                    WHEN overall_score >= 40 THEN "40-49"
                    WHEN overall_score >= 30 THEN "30-39"
                    WHEN overall_score >= 20 THEN "20-29"
                    WHEN overall_score >= 10 THEN "10-19"
                    ELSE "0-9"
                END as score_range,
                COUNT(*) as count
            ')
            ->groupBy('score_range')
            ->orderBy('score_range')
            ->get();

        return $distribution->pluck('count', 'score_range')->toArray();
    }

    /**
     * Get chain distribution data.
     */
    private function getChainDistribution(): array
    {
        $chainNames = [
            '1' => 'Ethereum',
            '56' => 'BSC',
            '42161' => 'Arbitrum',
            '137' => 'Polygon',
            '324' => 'zkSync Era',
            '59144' => 'Linea Mainnet',
            '8453' => 'Base',
            '534352' => 'Scroll',
            '10' => 'Optimism',
            '43114' => 'Avalanche',
            '250' => 'Fantom',
            '25' => 'Cronos',
            '66' => 'OKC',
            '128' => 'HECO',
            '100' => 'Gnosis',
            '10001' => 'ETHW',
            'tron' => 'Tron',
            '321' => 'KCC',
            '201022' => 'FON',
            '5000' => 'Mantle',
            '204' => 'opBNB',
            '42766' => 'ZKFair',
            '81457' => 'Blast',
            '169' => 'Manta Pacific',
            '80094' => 'Berachain',
            '2741' => 'Abstract',
            '177' => 'Hashkey Chain',
            '146' => 'Sonic',
            '1514' => 'Story',
        ];

        $distribution = Project::selectRaw('chain_id, COUNT(*) as count')
            ->groupBy('chain_id')
            ->get();

        $result = [];
        foreach ($distribution as $item) {
            $chainName = $chainNames[$item->chain_id] ?? 'Unknown';
            $result[$chainName] = $item->count;
        }

        return $result;
    }

    /**
     * Get risk levels distribution.
     */
    private function getRiskLevels(): array
    {
        $riskLevels = Project::where('status', 'completed')
            ->whereNotNull('overall_score')
            ->selectRaw('
                CASE 
                    WHEN overall_score >= 80 THEN "Low Risk"
                    WHEN overall_score >= 60 THEN "Medium Risk"
                    WHEN overall_score >= 40 THEN "High Risk"
                    ELSE "Very High Risk"
                END as risk_level,
                COUNT(*) as count
            ')
            ->groupBy('risk_level')
            ->get();

        return $riskLevels->pluck('count', 'risk_level')->toArray();
    }

    /**
     * Get analysis timeline data (last 30 days).
     */
    private function getAnalysisTimeline(): array
    {
        $timeline = Project::selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->where('created_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $result = [];
        for ($i = 29; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            $result[$date] = 0;
        }

        foreach ($timeline as $item) {
            $result[$item->date] = $item->count;
        }

        return $result;
    }

    /**
     * Get API status for external services.
     */
    public function apiStatus()
    {
        $status = [
            'openai' => $this->checkOpenAIStatus(),
            'goplus' => $this->checkGoPlusStatus(),
            'queue' => $this->checkQueueStatus(),
        ];

        return response()->json($status);
    }

    /**
     * Check OpenAI API status.
     */
    private function checkOpenAIStatus(): array
    {
        try {
            $apiKey = config('services.openai.api_key') ?? env('OPENAI_API_KEY');
            
            if (!$apiKey) {
                return ['status' => 'error', 'message' => 'API key not configured'];
            }

            // Check recent job failures
            $recentFailures = AnalysisJob::where('job_type', 'whitepaper_analysis')
                ->where('status', 'failed')
                ->where('created_at', '>=', now()->subHour())
                ->count();

            if ($recentFailures > 5) {
                return ['status' => 'warning', 'message' => 'High failure rate detected'];
            }

            return ['status' => 'ok', 'message' => 'Service operational'];

        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => $e->getMessage()];
        }
    }

    /**
     * Check GoPlus API status.
     */
    private function checkGoPlusStatus(): array
    {
        try {
            // Check recent job failures
            $recentFailures = AnalysisJob::where('job_type', 'security_scan')
                ->where('status', 'failed')
                ->where('created_at', '>=', now()->subHour())
                ->count();

            if ($recentFailures > 5) {
                return ['status' => 'warning', 'message' => 'High failure rate detected'];
            }

            return ['status' => 'ok', 'message' => 'Service operational'];

        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => $e->getMessage()];
        }
    }

    /**
     * Check queue status.
     */
    private function checkQueueStatus(): array
    {
        try {
            $pendingJobs = AnalysisJob::where('status', 'pending')->count();
            $processingJobs = AnalysisJob::where('status', 'processing')->count();

            if ($pendingJobs > 50) {
                return ['status' => 'warning', 'message' => "High queue load: {$pendingJobs} pending jobs"];
            }

            return [
                'status' => 'ok', 
                'message' => "Queue operational: {$pendingJobs} pending, {$processingJobs} processing"
            ];

        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => $e->getMessage()];
        }
    }
}
