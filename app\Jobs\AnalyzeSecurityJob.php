<?php

namespace App\Jobs;

use App\Models\AnalysisJob;
use App\Models\Project;
use App\Models\ScoringComponent;
use App\Services\GoPlusService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class AnalyzeSecurityJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 120; // 2 minutes
    public int $tries = 3;

    private Project $project;
    private AnalysisJob $analysisJob;

    /**
     * Create a new job instance.
     */
    public function __construct(Project $project, AnalysisJob $analysisJob)
    {
        $this->project = $project;
        $this->analysisJob = $analysisJob;
    }

    /**
     * Execute the job.
     */
    public function handle(GoPlusService $goPlusService): void
    {
        try {
            Log::info('Starting security analysis', [
                'project_id' => $this->project->id,
                'job_id' => $this->analysisJob->id,
                'contract_address' => $this->project->contract_address,
                'chain_id' => $this->project->chain_id
            ]);

            // Mark job as started
            $this->analysisJob->markAsStarted();



            // Analyze token security using GoPlus
            $securityAnalysis = $goPlusService->analyzeTokenSecurity(
                $this->project->contract_address,
                $this->project->chain_id
            );

            // Get additional token information
            $tokenInfo = $goPlusService->getTokenInfo(
                $this->project->contract_address,
                $this->project->chain_id
            );

            // Combine security and token info
            $combinedAnalysis = array_merge($securityAnalysis, [
                'token_info' => $tokenInfo
            ]);

            // Update project with security analysis and token info
            $this->project->update([
                'security_analysis' => $combinedAnalysis,
                'name' => $tokenInfo['token_name'] ?: $this->project->name,
                'symbol' => $tokenInfo['token_symbol'] ?: $this->project->symbol,
                'total_supply' => $this->parseSupply($tokenInfo['total_supply'] ?? null)
            ]);

            // Create scoring component for security
            $this->createSecurityScoringComponent($securityAnalysis);

            // Mark job as completed
            $this->analysisJob->markAsCompleted($combinedAnalysis);

            // Update project status if all analyses are complete
            $this->updateProjectStatus();

            Log::info('Security analysis completed', [
                'project_id' => $this->project->id,
                'security_score' => $securityAnalysis['score'] ?? 'N/A',
                'risk_factors_count' => count($securityAnalysis['risk_factors'] ?? [])
            ]);

        } catch (\Exception $e) {
            Log::error('Security analysis failed', [
                'project_id' => $this->project->id,
                'job_id' => $this->analysisJob->id,
                'error' => $e->getMessage()
            ]);

            // Mark job as failed
            $this->analysisJob->markAsFailed($e->getMessage());

            // Update project status
            $this->project->update([
                'status' => 'failed',
                'error_message' => 'Security analysis failed: ' . $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Create scoring component for security analysis.
     */
    private function createSecurityScoringComponent(array $analysis): void
    {
        $score = $analysis['score'] ?? 50;
        $details = [
            'is_honeypot' => $analysis['is_honeypot'] ?? false,
            'is_open_source' => $analysis['is_open_source'] ?? false,
            'is_mintable' => $analysis['is_mintable'] ?? false,
            'can_take_back_ownership' => $analysis['can_take_back_ownership'] ?? false,
            'owner_change_balance' => $analysis['owner_change_balance'] ?? false,
            'hidden_owner' => $analysis['hidden_owner'] ?? false,
            'selfdestruct' => $analysis['selfdestruct'] ?? false,
            'buy_tax' => $analysis['buy_tax'] ?? '0',
            'sell_tax' => $analysis['sell_tax'] ?? '0',
            'transfer_pausable' => $analysis['transfer_pausable'] ?? false,
            'trading_cooldown' => $analysis['trading_cooldown'] ?? false,
            'holder_count' => $analysis['holder_count'] ?? '0',
            'creator_percent' => $analysis['creator_percent'] ?? '0'
        ];

        $reasoning = $this->generateSecurityReasoning($analysis);

        ScoringComponent::updateOrCreate(
            [
                'project_id' => $this->project->id,
                'component_type' => 'security'
            ],
            [
                'score' => $score,
                'weight' => 40, // 40% weight as per specification
                'details' => $details,
                'reasoning' => $reasoning
            ]
        );
    }

    /**
     * Generate reasoning text for security score.
     */
    private function generateSecurityReasoning(array $analysis): string
    {
        $reasoning = [];
        
        if ($analysis['score'] >= 80) {
            $reasoning[] = "High security score indicates low risk factors.";
        } elseif ($analysis['score'] >= 60) {
            $reasoning[] = "Moderate security score with some concerns.";
        } else {
            $reasoning[] = "Low security score indicates significant risk factors.";
        }

        $riskFactors = $analysis['risk_factors'] ?? [];
        if (!empty($riskFactors)) {
            $reasoning[] = "Risk factors identified: " . implode(', ', array_slice($riskFactors, 0, 3));
            if (count($riskFactors) > 3) {
                $reasoning[] = "And " . (count($riskFactors) - 3) . " additional risk factors.";
            }
        }

        $warnings = $analysis['security_warnings'] ?? [];
        if (!empty($warnings)) {
            $reasoning[] = "Security warnings: " . implode(', ', array_slice($warnings, 0, 2));
        }

        if ($analysis['is_open_source'] ?? false) {
            $reasoning[] = "Contract source code is verified.";
        }

        return implode(' ', $reasoning);
    }

    /**
     * Parse total supply string to decimal.
     */
    private function parseSupply(?string $supply): ?float
    {
        if (!$supply) {
            return null;
        }

        // Remove any non-numeric characters except decimal point
        $cleaned = preg_replace('/[^0-9.]/', '', $supply);
        
        if (is_numeric($cleaned)) {
            return (float) $cleaned;
        }

        return null;
    }

    /**
     * Update project status based on completed analyses.
     */
    private function updateProjectStatus(): void
    {
        // Check if all required analyses are complete
        $completedJobs = $this->project->analysisJobs()
            ->where('status', 'completed')
            ->count();

        $totalJobs = $this->project->analysisJobs()->count();

        if ($completedJobs === $totalJobs) {
            // All analyses complete - calculate final score
            $overallScore = $this->project->calculateOverallScore();
            
            $this->project->update([
                'overall_score' => $overallScore,
                'status' => 'completed'
            ]);

            Log::info('Project analysis completed', [
                'project_id' => $this->project->id,
                'overall_score' => $overallScore
            ]);
        }
    }

    /**
     * Handle job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Security analysis job failed permanently', [
            'project_id' => $this->project->id,
            'job_id' => $this->analysisJob->id,
            'error' => $exception->getMessage(),
            'attempts' => $this->tries
        ]);

        // Mark job as failed if not already done
        if (!$this->analysisJob->hasFailed()) {
            $this->analysisJob->markAsFailed($exception->getMessage());
        }

        // Update project status
        $this->project->update([
            'status' => 'failed',
            'error_message' => 'Security analysis failed after ' . $this->tries . ' attempts: ' . $exception->getMessage()
        ]);
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'security-analysis',
            'project:' . $this->project->id,
            'contract:' . $this->project->contract_address,
            'chain:' . $this->project->chain_id
        ];
    }
}
