<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Project extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'symbol',
        'contract_address',
        'chain_id',
        'whitepaper_type',
        'whitepaper_path',
        'whitepaper_url',
        'openai_file_id',
        'whitepaper_content',
        'total_supply',
        'market_cap',
        'predicted_price',
        'overall_score',
        'security_analysis',
        'whitepaper_analysis',
        'tokenomics_analysis',
        'status',
        'error_message',
    ];

    protected $casts = [
        'total_supply' => 'decimal:2',
        'market_cap' => 'decimal:2',
        'predicted_price' => 'decimal:8',
        'overall_score' => 'integer',
        'security_analysis' => 'array',
        'whitepaper_analysis' => 'array',
        'tokenomics_analysis' => 'array',
    ];

    /**
     * Get the analysis jobs for the project.
     */
    public function analysisJobs(): HasMany
    {
        return $this->hasMany(AnalysisJob::class);
    }

    /**
     * Get the scoring components for the project.
     */
    public function scoringComponents(): HasMany
    {
        return $this->hasMany(ScoringComponent::class);
    }

    /**
     * Check if the project analysis is complete.
     */
    public function isAnalysisComplete(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if the project analysis is in progress.
     */
    public function isAnalyzing(): bool
    {
        return $this->status === 'analyzing';
    }

    /**
     * Check if the project analysis has failed.
     */
    public function hasFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Get the security score component.
     */
    public function getSecurityScore(): ?int
    {
        return $this->scoringComponents()
            ->where('component_type', 'security')
            ->value('score');
    }

    /**
     * Get the whitepaper score component.
     */
    public function getWhitepaperScore(): ?int
    {
        return $this->scoringComponents()
            ->where('component_type', 'whitepaper')
            ->value('score');
    }

    /**
     * Get the tokenomics score component.
     */
    public function getTokenomicsScore(): ?int
    {
        return $this->scoringComponents()
            ->where('component_type', 'tokenomics')
            ->value('score');
    }

    /**
     * Get the transparency score component.
     */
    public function getTransparencyScore(): ?int
    {
        return $this->scoringComponents()
            ->where('component_type', 'transparency')
            ->value('score');
    }

    /**
     * Calculate the overall score based on weighted components.
     */
    public function calculateOverallScore(): int
    {
        $components = $this->scoringComponents;
        
        if ($components->isEmpty()) {
            return 0;
        }

        $totalWeightedScore = 0;
        $totalWeight = 0;

        foreach ($components as $component) {
            $totalWeightedScore += $component->score * ($component->weight / 100);
            $totalWeight += $component->weight;
        }

        return $totalWeight > 0 ? round($totalWeightedScore / ($totalWeight / 100)) : 0;
    }

    /**
     * Get the contract explorer URL based on chain ID.
     */
    public function getExplorerUrl(): string
    {
        $explorers = [
            '1' => 'https://etherscan.io/address/',
            '56' => 'https://bscscan.com/address/',
            '42161' => 'https://arbiscan.io/address/',
            '137' => 'https://polygonscan.com/address/',
            '324' => 'https://explorer.zksync.io/address/',
            '59144' => 'https://lineascan.build/address/',
            '8453' => 'https://basescan.org/address/',
            '534352' => 'https://scrollscan.com/address/',
            '10' => 'https://optimistic.etherscan.io/address/',
            '43114' => 'https://snowtrace.io/address/',
            '250' => 'https://ftmscan.com/address/',
            '25' => 'https://cronoscan.com/address/',
            '66' => 'https://www.oklink.com/en/okc/address/',
            '128' => 'https://hecoinfo.com/address/',
            '100' => 'https://gnosisscan.io/address/',
            '10001' => 'https://www.oklink.com/en/ethw/address/',
            'tron' => 'https://tronscan.org/#/address/',
            '321' => 'https://explorer.kcc.io/address/',
            '201022' => 'https://fonscan.io/address/',
            '5000' => 'https://explorer.mantle.xyz/address/',
            '204' => 'https://opbnbscan.com/address/',
            '42766' => 'https://scan.zkfair.io/address/',
            '81457' => 'https://blastscan.io/address/',
            '169' => 'https://pacific-explorer.manta.network/address/',
            '80094' => 'https://bartio.beratrail.io/address/',
            '2741' => 'https://explorer.abs.xyz/address/',
            '177' => 'https://hashkeyscan.io/address/',
            '146' => 'https://explorer.soniclabs.com/address/',
            '1514' => 'https://explorer.story.foundation/address/',
        ];

        $baseUrl = $explorers[$this->chain_id] ?? 'https://etherscan.io/address/';
        return $baseUrl . $this->contract_address;
    }

    /**
     * Get the chain name based on chain ID.
     */
    public function getChainName(): string
    {
        $chains = [
            '1' => 'Ethereum',
            '56' => 'BSC',
            '42161' => 'Arbitrum',
            '137' => 'Polygon',
            '324' => 'zkSync Era',
            '59144' => 'Linea Mainnet',
            '8453' => 'Base',
            '534352' => 'Scroll',
            '10' => 'Optimism',
            '43114' => 'Avalanche',
            '250' => 'Fantom',
            '25' => 'Cronos',
            '66' => 'OKC',
            '128' => 'HECO',
            '100' => 'Gnosis',
            '10001' => 'ETHW',
            'tron' => 'Tron',
            '321' => 'KCC',
            '201022' => 'FON',
            '5000' => 'Mantle',
            '204' => 'opBNB',
            '42766' => 'ZKFair',
            '81457' => 'Blast',
            '169' => 'Manta Pacific',
            '80094' => 'Berachain',
            '2741' => 'Abstract',
            '177' => 'Hashkey Chain',
            '146' => 'Sonic',
            '1514' => 'Story',
        ];

        return $chains[$this->chain_id] ?? 'Unknown Chain';
    }
}
