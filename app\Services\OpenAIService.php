<?php

namespace App\Services;

use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class OpenAIService
{
    private string $apiKey;
    private string $baseUrl = 'https://api.openai.com/v1';

    public function __construct()
    {
        $this->apiKey = config('services.openai.api_key') ?? env('OPENAI_API_KEY');
        
        if (!$this->apiKey) {
            throw new \Exception('OpenAI API key is not configured');
        }
    }

    /**
     * Analyze whitepaper content using OpenAI GPT-4 (legacy method for text content).
     */
    public function analyzeWhitepaper(string $whitepaperContent): array
    {
        $prompt = $this->buildWhitepaperAnalysisPrompt($whitepaperContent);

        try {
            $response = $this->makeRequest('chat/completions', [
                'model' => 'gpt-4',
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'You are an expert cryptocurrency and blockchain analyst with deep knowledge of tokenomics, DeFi protocols, and crypto project evaluation.'
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'max_tokens' => 2000,
                'temperature' => 0.3,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $analysisText = $data['choices'][0]['message']['content'] ?? '';

                return $this->parseWhitepaperAnalysis($analysisText);
            }

            throw new \Exception('OpenAI API request failed: ' . $response->body());

        } catch (\Exception $e) {
            Log::error('OpenAI whitepaper analysis failed', [
                'error' => $e->getMessage(),
                'content_length' => strlen($whitepaperContent)
            ]);

            throw $e;
        }
    }

    /**
     * Upload PDF file directly to OpenAI and analyze it.
     */
    public function analyzeWhitepaperFromFile($filePath, string $fileName = null): array
    {
        try {
            // Upload file to OpenAI
            $fileId = $this->uploadFileToOpenAI($filePath, $fileName);

            // Analyze the uploaded file
            $analysis = $this->analyzeUploadedFile($fileId);

            return $analysis;

        } catch (\Exception $e) {
            Log::error('OpenAI file-based whitepaper analysis failed', [
                'error' => $e->getMessage(),
                'file_path' => $filePath
            ]);

            throw $e;
        }
    }

    /**
     * Analyze whitepaper from URL by sending URL directly to OpenAI.
     */
    public function analyzeWhitepaperFromUrl(string $url): array
    {
        try {
            $response = $this->makeRequest('chat/completions', [
                'model' => 'gpt-4o',
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'You are an expert cryptocurrency and blockchain analyst with deep knowledge of tokenomics, DeFi protocols, and crypto project evaluation.'
                    ],
                    [
                        'role' => 'user',
                        'content' => [
                            [
                                'type' => 'text',
                                'text' => $this->buildWhitepaperAnalysisPromptForFile()
                            ],
                            [
                                'type' => 'document',
                                'document' => [
                                    'url' => $url
                                ]
                            ]
                        ]
                    ]
                ],
                'max_tokens' => 2000,
                'temperature' => 0.3,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $analysisText = $data['choices'][0]['message']['content'] ?? '';

                return $this->parseWhitepaperAnalysis($analysisText);
            }

            throw new \Exception('OpenAI API request failed: ' . $response->body());

        } catch (\Exception $e) {
            Log::error('OpenAI URL-based whitepaper analysis failed', [
                'error' => $e->getMessage(),
                'url' => $url
            ]);

            throw $e;
        }
    }

    /**
     * Analyze tokenomics data using OpenAI.
     */
    public function analyzeTokenomics(array $tokenomicsData): array
    {
        $prompt = $this->buildTokenomicsAnalysisPrompt($tokenomicsData);

        try {
            $response = $this->makeRequest('chat/completions', [
                'model' => 'gpt-4',
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'You are a tokenomics expert specializing in analyzing token distribution, utility, and economic models of cryptocurrency projects.'
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'max_tokens' => 1500,
                'temperature' => 0.3,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $analysisText = $data['choices'][0]['message']['content'] ?? '';
                
                return $this->parseTokenomicsAnalysis($analysisText);
            }

            throw new \Exception('OpenAI API request failed: ' . $response->body());

        } catch (\Exception $e) {
            Log::error('OpenAI tokenomics analysis failed', [
                'error' => $e->getMessage(),
                'tokenomics_data' => $tokenomicsData
            ]);
            
            throw $e;
        }
    }

    /**
     * Build the whitepaper analysis prompt.
     */
    private function buildWhitepaperAnalysisPrompt(string $content): string
    {
        // Truncate content if too long to fit within token limits
        $maxContentLength = 8000; // Approximate character limit
        if (strlen($content) > $maxContentLength) {
            $content = substr($content, 0, $maxContentLength) . '... [TRUNCATED]';
        }

        return "Analyze this cryptocurrency whitepaper and provide a comprehensive evaluation. 

Please provide your analysis in the following JSON format:
{
    \"summary\": \"Brief 2-3 sentence summary of the project\",
    \"score\": 85,
    \"strengths\": [\"List of key strengths\"],
    \"weaknesses\": [\"List of concerns or weaknesses\"],
    \"technical_quality\": 80,
    \"team_credibility\": 75,
    \"innovation_level\": 90,
    \"market_potential\": 85,
    \"risk_factors\": [\"List of identified risks\"],
    \"recommendations\": \"Investment recommendation and key considerations\"
}

Score should be 1-100 based on:
- Technical soundness and innovation (25%)
- Team credibility and experience (20%)
- Market opportunity and competition (20%)
- Token utility and economics (20%)
- Roadmap clarity and feasibility (15%)

Whitepaper content:
{$content}";
    }

    /**
     * Build the tokenomics analysis prompt.
     */
    private function buildTokenomicsAnalysisPrompt(array $data): string
    {
        $dataJson = json_encode($data, JSON_PRETTY_PRINT);

        return "Analyze the tokenomics of this cryptocurrency project based on the provided data.

Please provide your analysis in the following JSON format:
{
    \"score\": 75,
    \"distribution_analysis\": \"Analysis of token distribution fairness\",
    \"utility_assessment\": \"Assessment of token utility and use cases\",
    \"inflation_analysis\": \"Analysis of token inflation/deflation mechanisms\",
    \"liquidity_concerns\": [\"List of liquidity-related concerns\"],
    \"strengths\": [\"Tokenomics strengths\"],
    \"weaknesses\": [\"Tokenomics weaknesses\"],
    \"sustainability_rating\": 80,
    \"recommendations\": \"Recommendations for improvement\"
}

Score should be 1-100 based on:
- Token distribution fairness (25%)
- Utility and demand drivers (25%)
- Supply mechanics and inflation control (20%)
- Liquidity and market making (15%)
- Long-term sustainability (15%)

Tokenomics data:
{$dataJson}";
    }

    /**
     * Parse the whitepaper analysis response.
     */
    private function parseWhitepaperAnalysis(string $analysisText): array
    {
        // Try to extract JSON from the response
        if (preg_match('/\{.*\}/s', $analysisText, $matches)) {
            $jsonData = json_decode($matches[0], true);
            if ($jsonData) {
                return $jsonData;
            }
        }

        // Fallback: create structured data from text
        return [
            'summary' => $this->extractSection($analysisText, 'summary') ?: 'Analysis completed',
            'score' => $this->extractScore($analysisText) ?: 50,
            'strengths' => $this->extractList($analysisText, 'strengths'),
            'weaknesses' => $this->extractList($analysisText, 'weaknesses'),
            'technical_quality' => 50,
            'team_credibility' => 50,
            'innovation_level' => 50,
            'market_potential' => 50,
            'risk_factors' => $this->extractList($analysisText, 'risks'),
            'recommendations' => $this->extractSection($analysisText, 'recommendations') ?: 'Further analysis recommended',
            'raw_analysis' => $analysisText
        ];
    }

    /**
     * Parse the tokenomics analysis response.
     */
    private function parseTokenomicsAnalysis(string $analysisText): array
    {
        // Try to extract JSON from the response
        if (preg_match('/\{.*\}/s', $analysisText, $matches)) {
            $jsonData = json_decode($matches[0], true);
            if ($jsonData) {
                return $jsonData;
            }
        }

        // Fallback: create structured data from text
        return [
            'score' => $this->extractScore($analysisText) ?: 50,
            'distribution_analysis' => $this->extractSection($analysisText, 'distribution') ?: 'Distribution analysis pending',
            'utility_assessment' => $this->extractSection($analysisText, 'utility') ?: 'Utility assessment pending',
            'inflation_analysis' => $this->extractSection($analysisText, 'inflation') ?: 'Inflation analysis pending',
            'liquidity_concerns' => $this->extractList($analysisText, 'liquidity'),
            'strengths' => $this->extractList($analysisText, 'strengths'),
            'weaknesses' => $this->extractList($analysisText, 'weaknesses'),
            'sustainability_rating' => 50,
            'recommendations' => $this->extractSection($analysisText, 'recommendations') ?: 'Further analysis recommended',
            'raw_analysis' => $analysisText
        ];
    }

    /**
     * Upload a file to OpenAI's files endpoint.
     */
    private function uploadFileToOpenAI($filePath, string $fileName = null): string
    {
        // Determine if filePath is a Laravel UploadedFile or a file path
        if (is_string($filePath)) {
            // It's a file path
            if (!file_exists($filePath)) {
                throw new \Exception("File not found: {$filePath}");
            }
            $fileContent = file_get_contents($filePath);
            $fileName = $fileName ?: basename($filePath);
        } else {
            // It's an UploadedFile
            $fileContent = $filePath->getContent();
            $fileName = $fileName ?: $filePath->getClientOriginalName();
        }

        $response = Http::withToken($this->apiKey)
            ->timeout(120) // Longer timeout for file uploads
            ->attach('file', $fileContent, $fileName)
            ->post("{$this->baseUrl}/files", [
                'purpose' => 'vision'
            ]);

        if ($response->successful()) {
            $data = $response->json();
            return $data['id'] ?? throw new \Exception('No file ID returned from OpenAI');
        }

        // Enhanced error handling for different failure scenarios
        $statusCode = $response->status();
        $responseBody = $response->body();

        switch ($statusCode) {
            case 400:
                throw new \Exception('Invalid file format or corrupted PDF. Please ensure your file is a valid PDF document.');
            case 413:
                throw new \Exception('File size too large. OpenAI has a file size limit. Please use a smaller PDF.');
            case 429:
                throw new \Exception('Rate limit exceeded. Please try again in a few minutes.');
            case 401:
                throw new \Exception('OpenAI API authentication failed. Please check your API key configuration.');
            case 403:
                throw new \Exception('Access denied. Your OpenAI account may not have permission to upload files.');
            default:
                throw new \Exception("File upload to OpenAI failed (HTTP {$statusCode}): " . $responseBody);
        }
    }

    /**
     * Analyze an uploaded file using its file ID.
     */
    private function analyzeUploadedFile(string $fileId): array
    {
        $response = $this->makeRequest('chat/completions', [
            'model' => 'gpt-4o',
            'messages' => [
                [
                    'role' => 'system',
                    'content' => 'You are an expert cryptocurrency and blockchain analyst with deep knowledge of tokenomics, DeFi protocols, and crypto project evaluation.'
                ],
                [
                    'role' => 'user',
                    'content' => [
                        [
                            'type' => 'text',
                            'text' => $this->buildWhitepaperAnalysisPromptForFile()
                        ],
                        [
                            'type' => 'document',
                            'document' => [
                                'file_id' => $fileId
                            ]
                        ]
                    ]
                ]
            ],
            'max_tokens' => 2000,
            'temperature' => 0.3,
        ]);

        if ($response->successful()) {
            $data = $response->json();
            $analysisText = $data['choices'][0]['message']['content'] ?? '';

            return $this->parseWhitepaperAnalysis($analysisText);
        }

        // Enhanced error handling for analysis failures
        $statusCode = $response->status();
        $responseBody = $response->body();

        switch ($statusCode) {
            case 400:
                throw new \Exception('Invalid request format or unsupported file type for analysis.');
            case 429:
                throw new \Exception('Rate limit exceeded. Please try again in a few minutes.');
            case 401:
                throw new \Exception('OpenAI API authentication failed during analysis.');
            case 403:
                throw new \Exception('Access denied. Your OpenAI account may not have permission for this analysis.');
            case 404:
                throw new \Exception('File not found on OpenAI servers. The file may have expired or been deleted.');
            default:
                throw new \Exception("OpenAI analysis request failed (HTTP {$statusCode}): " . $responseBody);
        }
    }

    /**
     * Build the whitepaper analysis prompt for file-based analysis.
     */
    private function buildWhitepaperAnalysisPromptForFile(): string
    {
        return "Analyze this cryptocurrency whitepaper document and provide a comprehensive evaluation.

Please provide your analysis in the following JSON format:
{
    \"summary\": \"Brief 2-3 sentence summary of the project\",
    \"score\": 85,
    \"strengths\": [\"List of key strengths\"],
    \"weaknesses\": [\"List of concerns or weaknesses\"],
    \"technical_quality\": 80,
    \"team_credibility\": 75,
    \"innovation_level\": 90,
    \"market_potential\": 85,
    \"risk_factors\": [\"List of identified risks\"],
    \"recommendations\": \"Investment recommendation and key considerations\"
}

Score should be 1-100 based on:
- Technical soundness and innovation (25%)
- Team credibility and experience (20%)
- Market opportunity and competition (20%)
- Token utility and economics (20%)
- Roadmap clarity and feasibility (15%)

Please analyze the entire document thoroughly, including any charts, diagrams, or technical specifications.";
    }

    /**
     * Make HTTP request to OpenAI API.
     */
    private function makeRequest(string $endpoint, array $data): Response
    {
        return Http::withToken($this->apiKey)
            ->timeout(60)
            ->post("{$this->baseUrl}/{$endpoint}", $data);
    }

    /**
     * Extract a specific section from analysis text.
     */
    private function extractSection(string $text, string $section): ?string
    {
        $pattern = "/{$section}[:\s]*([^\n]+)/i";
        if (preg_match($pattern, $text, $matches)) {
            return trim($matches[1]);
        }
        return null;
    }

    /**
     * Extract score from analysis text.
     */
    private function extractScore(string $text): ?int
    {
        if (preg_match('/score[:\s]*(\d+)/i', $text, $matches)) {
            return (int) $matches[1];
        }
        return null;
    }

    /**
     * Extract list items from analysis text.
     */
    private function extractList(string $text, string $listType): array
    {
        $pattern = "/{$listType}[:\s]*\n?((?:[-*•]\s*[^\n]+\n?)+)/i";
        if (preg_match($pattern, $text, $matches)) {
            $items = preg_split('/[-*•]\s*/', $matches[1]);
            return array_filter(array_map('trim', $items));
        }
        return [];
    }
}
