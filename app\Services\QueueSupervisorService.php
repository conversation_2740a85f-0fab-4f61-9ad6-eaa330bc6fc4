<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Process;
use Illuminate\Support\Facades\Cache;

class QueueSupervisorService
{
    private string $projectPath;
    private string $phpPath;
    private string $logPath;

    public function __construct()
    {
        $this->projectPath = base_path();
        $this->phpPath = PHP_BINARY;
        $this->logPath = storage_path('logs');
    }

    /**
     * Check if queue workers are running.
     */
    public function areWorkersRunning(): bool
    {
        if (PHP_OS_FAMILY === 'Windows') {
            return $this->areWindowsWorkersRunning();
        }
        
        return $this->areUnixWorkersRunning();
    }

    /**
     * Start queue workers.
     */
    public function startWorkers(int $workerCount = 2): array
    {
        try {
            $results = [];

            for ($i = 1; $i <= $workerCount; $i++) {
                $result = $this->startWorker($i);
                $results[] = $result;
            }

            // Cache worker status
            Cache::put('queue_workers_status', [
                'running' => true,
                'count' => $workerCount,
                'started_at' => now(),
            ], 3600);

            return $results;
        } catch (\Exception $e) {
            Log::error('Failed to start workers', ['error' => $e->getMessage()]);
            throw new \Exception('Failed to start workers: ' . $e->getMessage());
        }
    }

    /**
     * Stop all queue workers.
     */
    public function stopWorkers(): bool
    {
        try {
            if (PHP_OS_FAMILY === 'Windows') {
                $this->stopWindowsWorkers();
            } else {
                $this->stopUnixWorkers();
            }

            Cache::forget('queue_workers_status');
            
            Log::info('Queue workers stopped successfully');
            return true;

        } catch (\Exception $e) {
            Log::error('Failed to stop queue workers', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Restart queue workers.
     */
    public function restartWorkers(int $workerCount = 2): array
    {
        $this->stopWorkers();
        sleep(2); // Give workers time to stop
        return $this->startWorkers($workerCount);
    }

    /**
     * Get worker status information.
     */
    public function getWorkerStatus(): array
    {
        $isRunning = $this->areWorkersRunning();
        $cachedStatus = Cache::get('queue_workers_status', []);

        return [
            'running' => $isRunning,
            'count' => $this->getRunningWorkerCount(),
            'started_at' => $cachedStatus['started_at'] ?? null,
            'uptime' => $cachedStatus['started_at'] ? now()->diffForHumans($cachedStatus['started_at']) : null,
            'log_files' => $this->getLogFiles(),
        ];
    }

    /**
     * Start a single worker.
     */
    private function startWorker(int $workerId): array
    {
        try {
            $command = sprintf(
                '%s %s/artisan queue:work --sleep=3 --tries=3 --max-time=3600',
                $this->phpPath,
                $this->projectPath
            );

            $logFile = $this->logPath . "/worker{$workerId}.log";

            if (PHP_OS_FAMILY === 'Windows') {
                $fullCommand = sprintf(
                    'start "Laravel Worker %d" /min cmd /c "%s >> %s 2>&1"',
                    $workerId,
                    $command,
                    $logFile
                );
                exec($fullCommand);
            } else {
                $fullCommand = sprintf(
                    'nohup %s >> %s 2>&1 & echo $!',
                    $command,
                    $logFile
                );
                $pid = exec($fullCommand);
                file_put_contents($this->logPath . "/worker{$workerId}.pid", $pid);
            }

            Log::info("Started queue worker {$workerId}", ['command' => $command]);

            return [
                'worker_id' => $workerId,
                'status' => 'started',
                'log_file' => $logFile,
            ];

        } catch (\Exception $e) {
            Log::error("Failed to start worker {$workerId}", ['error' => $e->getMessage()]);
            
            return [
                'worker_id' => $workerId,
                'status' => 'failed',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check if Windows workers are running.
     */
    private function areWindowsWorkersRunning(): bool
    {
        $output = shell_exec('tasklist /fi "windowtitle eq Laravel Worker*" 2>nul');
        return $output && strpos($output, 'cmd.exe') !== false;
    }

    /**
     * Check if Unix workers are running.
     */
    private function areUnixWorkersRunning(): bool
    {
        $output = shell_exec('pgrep -f "queue:work" 2>/dev/null');
        return !empty(trim($output));
    }

    /**
     * Stop Windows workers.
     */
    private function stopWindowsWorkers(): void
    {
        exec('taskkill /f /fi "windowtitle eq Laravel Worker*" 2>nul');
    }

    /**
     * Stop Unix workers.
     */
    private function stopUnixWorkers(): void
    {
        // Kill by process name
        exec('pkill -f "queue:work" 2>/dev/null');
        
        // Also kill by PID files if they exist
        $pidFiles = glob($this->logPath . '/worker*.pid');
        foreach ($pidFiles as $pidFile) {
            $pid = trim(file_get_contents($pidFile));
            if ($pid && is_numeric($pid)) {
                exec("kill {$pid} 2>/dev/null");
            }
            unlink($pidFile);
        }
    }

    /**
     * Get count of running workers.
     */
    private function getRunningWorkerCount(): int
    {
        if (PHP_OS_FAMILY === 'Windows') {
            $output = shell_exec('tasklist /fi "windowtitle eq Laravel Worker*" 2>nul');
            return substr_count($output ?: '', 'cmd.exe');
        } else {
            $output = shell_exec('pgrep -f "queue:work" 2>/dev/null | wc -l');
            return (int) trim($output ?: '0');
        }
    }

    /**
     * Get available log files.
     */
    private function getLogFiles(): array
    {
        $logFiles = [];
        $files = glob($this->logPath . '/worker*.log');
        
        foreach ($files as $file) {
            $logFiles[] = [
                'name' => basename($file),
                'path' => $file,
                'size' => filesize($file),
                'modified' => filemtime($file),
            ];
        }

        return $logFiles;
    }
}
