<?php
/**
 * Debug script to test queue management functionality
 * Run this to identify issues: php debug-queue.php
 */

echo "=== Queue Management Debug Script ===\n\n";

// Test 1: Check if we can load <PERSON><PERSON>
echo "1. Testing Laravel bootstrap...\n";
try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    echo "   ✅ Laravel loaded successfully\n\n";
} catch (Exception $e) {
    echo "   ❌ Laravel failed to load: " . $e->getMessage() . "\n\n";
    exit(1);
}

// Test 2: Check if QueueSupervisorService can be instantiated
echo "2. Testing QueueSupervisorService...\n";
try {
    $supervisor = new App\Services\QueueSupervisorService();
    echo "   ✅ QueueSupervisorService created successfully\n\n";
} catch (Exception $e) {
    echo "   ❌ QueueSupervisorService failed: " . $e->getMessage() . "\n\n";
    exit(1);
}

// Test 3: Check basic methods
echo "3. Testing basic methods...\n";
try {
    $status = $supervisor->getWorkerStatus();
    echo "   ✅ getWorkerStatus() works\n";
    echo "   Running: " . ($status['running'] ? 'Yes' : 'No') . "\n";
    echo "   Count: " . $status['count'] . "\n\n";
} catch (Exception $e) {
    echo "   ❌ getWorkerStatus() failed: " . $e->getMessage() . "\n\n";
}

// Test 4: Check if we can create the controller
echo "4. Testing DashboardController...\n";
try {
    $controller = new App\Http\Controllers\DashboardController($supervisor);
    echo "   ✅ DashboardController created successfully\n\n";
} catch (Exception $e) {
    echo "   ❌ DashboardController failed: " . $e->getMessage() . "\n\n";
}

// Test 5: Test the actual method that's failing
echo "5. Testing startWorkers method...\n";
try {
    $request = new Illuminate\Http\Request();
    $request->merge(['workers' => 2]);
    
    $response = $controller->startWorkers($request);
    $content = $response->getContent();
    
    echo "   Response content: " . $content . "\n";
    
    $data = json_decode($content, true);
    if ($data === null) {
        echo "   ❌ Response is not valid JSON\n";
    } else {
        echo "   ✅ Response is valid JSON\n";
        echo "   Success: " . ($data['success'] ? 'true' : 'false') . "\n";
        echo "   Message: " . $data['message'] . "\n";
    }
} catch (Exception $e) {
    echo "   ❌ startWorkers() failed: " . $e->getMessage() . "\n";
    echo "   Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Debug Complete ===\n";
echo "\nNext steps:\n";
echo "1. Check the Laravel logs: storage/logs/laravel.log\n";
echo "2. Test the endpoint directly: curl -X POST http://your-domain/queue/test\n";
echo "3. Check browser console for detailed error messages\n";
echo "4. Verify your .env configuration\n";
