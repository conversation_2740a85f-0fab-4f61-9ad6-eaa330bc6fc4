@extends('layouts.app')

@section('title', $project->name . ' - Analysis Results')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" x-data="analysisPage({{ $project->id }})">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">{{ $project->name }}</h1>
                <div class="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                    <span>{{ $project->getChainName() }}</span>
                    <span>•</span>
                    <span>{{ $project->created_at->format('M j, Y') }}</span>
                    <span>•</span>
                    <a href="{{ $project->getExplorerUrl() }}" target="_blank" class="text-indigo-600 hover:text-indigo-800">
                        View Contract ↗
                    </a>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <!-- Status Badge -->
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                      :class="{
                          'bg-green-100 text-green-800': status === 'completed',
                          'bg-yellow-100 text-yellow-800': status === 'analyzing',
                          'bg-red-100 text-red-800': status === 'failed',
                          'bg-gray-100 text-gray-800': status === 'pending'
                      }">
                    <span x-text="status.charAt(0).toUpperCase() + status.slice(1)"></span>
                </span>

                <!-- Overall Score -->
                <div x-show="overallScore !== null" class="text-center">
                    <div class="text-2xl font-bold" 
                         :class="{
                             'text-green-600': overallScore >= 80,
                             'text-yellow-600': overallScore >= 60 && overallScore < 80,
                             'text-orange-600': overallScore >= 40 && overallScore < 60,
                             'text-red-600': overallScore < 40
                         }">
                        <span x-text="overallScore"></span>
                    </div>
                    <div class="text-xs text-gray-500">Overall Score</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Progress Bar (shown when analyzing) -->
    <div x-show="status === 'analyzing'" class="mb-8">
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center justify-between mb-2">
                <h3 class="text-lg font-medium text-gray-900">Analysis Progress</h3>
                <span class="text-sm text-gray-500" x-text="progress + '%'"></span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-indigo-600 h-2 rounded-full transition-all duration-300" 
                     :style="'width: ' + progress + '%'"></div>
            </div>
            <div class="mt-4 space-y-2">
                <template x-for="job in jobs" :key="job.type">
                    <div class="flex items-center justify-between text-sm">
                        <span x-text="formatJobType(job.type)" class="text-gray-700"></span>
                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium"
                              :class="{
                                  'bg-green-100 text-green-800': job.status === 'completed',
                                  'bg-yellow-100 text-yellow-800': job.status === 'processing',
                                  'bg-red-100 text-red-800': job.status === 'failed',
                                  'bg-gray-100 text-gray-800': job.status === 'pending'
                              }">
                            <span x-text="job.status.charAt(0).toUpperCase() + job.status.slice(1)"></span>
                        </span>
                    </div>
                </template>
            </div>
        </div>
    </div>

    <!-- Error Message -->
    <div x-show="status === 'failed'" class="mb-8">
        <div class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Analysis Failed</h3>
                    <div class="mt-2 text-sm text-red-700">
                        <p>{{ $project->error_message ?? 'An error occurred during analysis.' }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Results (shown when completed) -->
    <div x-show="status === 'completed'" class="space-y-8">
        <!-- Scoring Components -->
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            <template x-for="component in scoringComponents" :key="component.type">
                <div class="bg-white overflow-hidden shadow-sm rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 rounded-md flex items-center justify-center"
                                     :class="{
                                         'bg-green-100': component.score >= 80,
                                         'bg-yellow-100': component.score >= 60 && component.score < 80,
                                         'bg-orange-100': component.score >= 40 && component.score < 60,
                                         'bg-red-100': component.score < 40
                                     }">
                                    <span class="text-sm font-medium"
                                          :class="{
                                              'text-green-600': component.score >= 80,
                                              'text-yellow-600': component.score >= 60 && component.score < 80,
                                              'text-orange-600': component.score >= 40 && component.score < 60,
                                              'text-red-600': component.score < 40
                                          }"
                                          x-text="component.grade"></span>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate" x-text="formatComponentType(component.type)"></dt>
                                    <dd>
                                        <div class="text-lg font-medium text-gray-900" x-text="component.score + '/100'"></div>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </div>

        <!-- Detailed Analysis Sections -->
        <div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
            <!-- Security Analysis -->
            @if($project->security_analysis)
            <div class="bg-white shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">🛡️ Security Analysis</h3>
                </div>
                <div class="p-6">
                    @php $security = $project->security_analysis; @endphp
                    
                    <!-- Risk Factors -->
                    @if(!empty($security['risk_factors']))
                    <div class="mb-6">
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Risk Factors</h4>
                        <ul class="space-y-2">
                            @foreach($security['risk_factors'] as $risk)
                            <li class="flex items-start">
                                <svg class="flex-shrink-0 h-4 w-4 text-red-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                                <span class="ml-2 text-sm text-gray-700">{{ $risk }}</span>
                            </li>
                            @endforeach
                        </ul>
                    </div>
                    @endif

                    <!-- Security Metrics -->
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-500">Open Source:</span>
                            <span class="{{ ($security['is_open_source'] ?? false) ? 'text-green-600' : 'text-red-600' }}">
                                {{ ($security['is_open_source'] ?? false) ? 'Yes' : 'No' }}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500">Honeypot:</span>
                            <span class="{{ ($security['is_honeypot'] ?? false) ? 'text-red-600' : 'text-green-600' }}">
                                {{ ($security['is_honeypot'] ?? false) ? 'Yes' : 'No' }}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500">Buy Tax:</span>
                            <span class="text-gray-900">{{ number_format(($security['buy_tax'] ?? 0) * 100, 1) }}%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500">Sell Tax:</span>
                            <span class="text-gray-900">{{ number_format(($security['sell_tax'] ?? 0) * 100, 1) }}%</span>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Whitepaper Analysis -->
            @if($project->whitepaper_analysis)
            <div class="bg-white shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">📄 Whitepaper Analysis</h3>
                </div>
                <div class="p-6">
                    @php $whitepaper = $project->whitepaper_analysis; @endphp
                    
                    <!-- Summary -->
                    @if(!empty($whitepaper['summary']))
                    <div class="mb-6">
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Summary</h4>
                        <p class="text-sm text-gray-700">{{ $whitepaper['summary'] }}</p>
                    </div>
                    @endif

                    <!-- Strengths and Weaknesses -->
                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                        @if(!empty($whitepaper['strengths']))
                        <div>
                            <h4 class="text-sm font-medium text-green-800 mb-2">Strengths</h4>
                            <ul class="space-y-1">
                                @foreach(array_slice($whitepaper['strengths'], 0, 3) as $strength)
                                <li class="flex items-start">
                                    <svg class="flex-shrink-0 h-4 w-4 text-green-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                    </svg>
                                    <span class="ml-2 text-sm text-gray-700">{{ $strength }}</span>
                                </li>
                                @endforeach
                            </ul>
                        </div>
                        @endif

                        @if(!empty($whitepaper['weaknesses']))
                        <div>
                            <h4 class="text-sm font-medium text-red-800 mb-2">Concerns</h4>
                            <ul class="space-y-1">
                                @foreach(array_slice($whitepaper['weaknesses'], 0, 3) as $weakness)
                                <li class="flex items-start">
                                    <svg class="flex-shrink-0 h-4 w-4 text-red-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                    </svg>
                                    <span class="ml-2 text-sm text-gray-700">{{ $weakness }}</span>
                                </li>
                                @endforeach
                            </ul>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Price Prediction -->
        @if($project->tokenomics_analysis && !empty($project->tokenomics_analysis['price_predictions']))
        <div class="bg-white shadow-sm rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">💰 Price Predictions</h3>
                <p class="text-sm text-gray-500 mt-1">Speculative predictions based on tokenomics analysis</p>
            </div>
            <div class="p-6">
                @php $predictions = $project->tokenomics_analysis['price_predictions']; @endphp
                
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                    @foreach(['conservative_1_year', 'moderate_1_year', 'optimistic_1_year', 'bull_market_1_year'] as $scenario)
                        @if(isset($predictions[$scenario]))
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-lg font-semibold text-gray-900">
                                ${{ number_format($predictions[$scenario], 8) }}
                            </div>
                            <div class="text-sm text-gray-500 capitalize">
                                {{ str_replace('_1_year', '', $scenario) }}
                            </div>
                        </div>
                        @endif
                    @endforeach
                </div>

                @if(!empty($predictions['disclaimer']))
                <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <p class="text-sm text-yellow-800">
                        <strong>Disclaimer:</strong> {{ $predictions['disclaimer'] }}
                    </p>
                </div>
                @endif
            </div>
        </div>
        @endif
    </div>
</div>

@push('scripts')
<script>
function analysisPage(projectId) {
    return {
        projectId: projectId,
        status: '{{ $project->status }}',
        overallScore: {{ $project->overall_score ?? 'null' }},
        progress: {{ $project->status === 'analyzing' ? 0 : 100 }},
        jobs: {!! json_encode($project->analysisJobs->map(function($job) {
            return [
                'type' => $job->job_type,
                'status' => $job->status,
                'duration' => $job->getFormattedDuration(),
                'error' => $job->error_message
            ];
        })->toArray()) !!},
        scoringComponents: {!! json_encode($project->scoringComponents->map(function($component) {
            return [
                'type' => $component->component_type,
                'score' => $component->score,
                'weight' => $component->weight,
                'grade' => $component->getGrade(),
                'color' => $component->getScoreColor()
            ];
        })->toArray()) !!},

        init() {
            if (this.status === 'analyzing') {
                this.pollStatus();
            }
        },

        async pollStatus() {
            try {
                const response = await fetch(`/analysis/${this.projectId}/status`, {
                    headers: {
                        'X-CSRF-TOKEN': window.csrfToken,
                        'Accept': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    this.status = data.status;
                    this.overallScore = data.overall_score;
                    this.progress = data.progress;
                    this.jobs = data.jobs;
                    this.scoringComponents = data.scoring_components;

                    if (this.status === 'analyzing') {
                        setTimeout(() => this.pollStatus(), 3000);
                    } else if (this.status === 'completed') {
                        // Reload page to show full results
                        window.location.reload();
                    }
                }
            } catch (error) {
                console.error('Error polling status:', error);
                setTimeout(() => this.pollStatus(), 5000);
            }
        },

        formatJobType(type) {
            const types = {
                'security_scan': 'Security Scan',
                'whitepaper_analysis': 'Whitepaper Analysis',
                'price_prediction': 'Price Prediction'
            };
            return types[type] || type;
        },

        formatComponentType(type) {
            const types = {
                'security': 'Security',
                'whitepaper': 'Whitepaper',
                'tokenomics': 'Tokenomics',
                'transparency': 'Transparency'
            };
            return types[type] || type;
        }
    }
}
</script>
@endpush
@endsection
