<?php

use App\Http\Controllers\AnalysisController;
use App\Http\Controllers\DashboardController;
use Illuminate\Support\Facades\Route;

// Dashboard
Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
Route::get('/api-status', [DashboardController::class, 'apiStatus'])->name('api.status');

// Queue Management Routes
Route::prefix('queue')->name('queue.')->group(function () {
    Route::post('/start', [DashboardController::class, 'startWorkers'])->name('start');
    Route::post('/stop', [DashboardController::class, 'stopWorkers'])->name('stop');
    Route::post('/restart', [DashboardController::class, 'restartWorkers'])->name('restart');
    Route::get('/status', [DashboardController::class, 'getWorkerStatus'])->name('status');
});

// Analysis Routes
Route::prefix('analysis')->name('analysis.')->group(function () {
    Route::get('/', [AnalysisController::class, 'index'])->name('index');
    Route::post('/', [AnalysisController::class, 'store'])->name('store');
    Route::get('/list', [AnalysisController::class, 'list'])->name('list');
    Route::get('/{project}', [AnalysisController::class, 'show'])->name('show');
    Route::get('/{project}/status', [AnalysisController::class, 'status'])->name('status');
    Route::delete('/{project}', [AnalysisController::class, 'destroy'])->name('destroy');
});
