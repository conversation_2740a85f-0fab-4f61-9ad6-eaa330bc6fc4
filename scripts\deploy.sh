#!/bin/bash

# Laravel Queue Worker Deployment Script
# This script handles deployment and queue worker management for production

set -e

PROJECT_PATH="/path/to/your/project"
PHP_PATH="/usr/bin/php"
USER="www-data"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if supervisor is installed
check_supervisor() {
    if ! command -v supervisorctl &> /dev/null; then
        print_error "Supervisor is not installed. Installing..."
        sudo apt-get update
        sudo apt-get install -y supervisor
    fi
}

# Function to deploy application
deploy() {
    print_status "Starting deployment..."
    
    cd $PROJECT_PATH
    
    # Pull latest code
    print_status "Pulling latest code..."
    git pull origin main
    
    # Install/update dependencies
    print_status "Installing dependencies..."
    composer install --no-dev --optimize-autoloader
    
    # Run migrations
    print_status "Running migrations..."
    php artisan migrate --force
    
    # Clear caches
    print_status "Clearing caches..."
    php artisan config:cache
    php artisan route:cache
    php artisan view:cache
    
    # Restart queue workers
    print_status "Restarting queue workers..."
    php artisan queue:supervisor restart --workers=3
    
    print_status "Deployment completed successfully!"
}

# Function to setup supervisor configuration
setup_supervisor() {
    print_status "Setting up supervisor configuration..."
    
    # Copy supervisor config
    sudo cp supervisor/laravel-worker.conf /etc/supervisor/conf.d/
    
    # Update paths in config
    sudo sed -i "s|/path/to/your/project|$PROJECT_PATH|g" /etc/supervisor/conf.d/laravel-worker.conf
    sudo sed -i "s|user=www-data|user=$USER|g" /etc/supervisor/conf.d/laravel-worker.conf
    
    # Reload supervisor
    sudo supervisorctl reread
    sudo supervisorctl update
    
    print_status "Supervisor configuration updated!"
}

# Function to start workers
start_workers() {
    print_status "Starting queue workers..."
    
    if command -v supervisorctl &> /dev/null; then
        sudo supervisorctl start laravel-worker:*
    else
        php artisan queue:supervisor start --workers=2
    fi
    
    print_status "Queue workers started!"
}

# Function to stop workers
stop_workers() {
    print_status "Stopping queue workers..."
    
    if command -v supervisorctl &> /dev/null; then
        sudo supervisorctl stop laravel-worker:*
    else
        php artisan queue:supervisor stop
    fi
    
    print_status "Queue workers stopped!"
}

# Function to restart workers
restart_workers() {
    print_status "Restarting queue workers..."
    
    if command -v supervisorctl &> /dev/null; then
        sudo supervisorctl restart laravel-worker:*
    else
        php artisan queue:supervisor restart --workers=2
    fi
    
    print_status "Queue workers restarted!"
}

# Function to show status
show_status() {
    print_status "Queue worker status:"
    
    if command -v supervisorctl &> /dev/null; then
        sudo supervisorctl status laravel-worker:*
    else
        php artisan queue:supervisor status
    fi
}

# Main script logic
case "$1" in
    deploy)
        deploy
        ;;
    setup)
        check_supervisor
        setup_supervisor
        ;;
    start)
        start_workers
        ;;
    stop)
        stop_workers
        ;;
    restart)
        restart_workers
        ;;
    status)
        show_status
        ;;
    *)
        echo "Usage: $0 {deploy|setup|start|stop|restart|status}"
        echo ""
        echo "Commands:"
        echo "  deploy   - Full deployment with code update and worker restart"
        echo "  setup    - Setup supervisor configuration"
        echo "  start    - Start queue workers"
        echo "  stop     - Stop queue workers"
        echo "  restart  - Restart queue workers"
        echo "  status   - Show worker status"
        exit 1
        ;;
esac

exit 0
