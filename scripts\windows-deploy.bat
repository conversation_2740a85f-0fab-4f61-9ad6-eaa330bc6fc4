@echo off
REM Windows deployment script for Laravel Queue Workers
REM This script handles deployment and queue worker management on Windows

setlocal enabledelayedexpansion

set PROJECT_PATH=c:\xampp\htdocs\ORACLE
set PHP_PATH=c:\xampp\php\php.exe

REM Function to print status messages
:print_status
echo [INFO] %~1
goto :eof

:print_error
echo [ERROR] %~1
goto :eof

REM Function to deploy application
:deploy
call :print_status "Starting deployment..."

cd /d %PROJECT_PATH%

REM Pull latest code (if using git)
call :print_status "Pulling latest code..."
git pull origin main

REM Install/update dependencies
call :print_status "Installing dependencies..."
composer install --no-dev --optimize-autoloader

REM Run migrations
call :print_status "Running migrations..."
%PHP_PATH% artisan migrate --force

REM Clear caches
call :print_status "Clearing caches..."
%PHP_PATH% artisan config:cache
%PHP_PATH% artisan route:cache
%PHP_PATH% artisan view:cache

REM Restart queue workers
call :print_status "Restarting queue workers..."
%PHP_PATH% artisan queue:supervisor restart --workers=2

call :print_status "Deployment completed successfully!"
goto :eof

REM Function to start workers
:start_workers
call :print_status "Starting queue workers..."
%PHP_PATH% artisan queue:supervisor start --workers=2
call :print_status "Queue workers started!"
goto :eof

REM Function to stop workers
:stop_workers
call :print_status "Stopping queue workers..."
%PHP_PATH% artisan queue:supervisor stop
call :print_status "Queue workers stopped!"
goto :eof

REM Function to restart workers
:restart_workers
call :print_status "Restarting queue workers..."
%PHP_PATH% artisan queue:supervisor restart --workers=2
call :print_status "Queue workers restarted!"
goto :eof

REM Function to show status
:show_status
call :print_status "Queue worker status:"
%PHP_PATH% artisan queue:supervisor status
goto :eof

REM Function to install as Windows service (requires NSSM)
:install_service
call :print_status "Installing queue workers as Windows service..."

REM Check if NSSM is available
where nssm >nul 2>nul
if %errorlevel% neq 0 (
    call :print_error "NSSM not found. Please install NSSM to run as Windows service."
    call :print_error "Download from: https://nssm.cc/download"
    goto :eof
)

REM Install service
nssm install LaravelQueueWorker "%PHP_PATH%" "%PROJECT_PATH%\artisan queue:work --sleep=3 --tries=3 --max-time=3600"
nssm set LaravelQueueWorker AppDirectory "%PROJECT_PATH%"
nssm set LaravelQueueWorker DisplayName "Laravel Queue Worker"
nssm set LaravelQueueWorker Description "Laravel Queue Worker Service"
nssm set LaravelQueueWorker Start SERVICE_AUTO_START

call :print_status "Service installed. Use 'sc start LaravelQueueWorker' to start."
goto :eof

REM Function to uninstall Windows service
:uninstall_service
call :print_status "Uninstalling queue worker service..."
nssm stop LaravelQueueWorker
nssm remove LaravelQueueWorker confirm
call :print_status "Service uninstalled!"
goto :eof

REM Main script logic
if "%1"=="deploy" (
    call :deploy
) else if "%1"=="start" (
    call :start_workers
) else if "%1"=="stop" (
    call :stop_workers
) else if "%1"=="restart" (
    call :restart_workers
) else if "%1"=="status" (
    call :show_status
) else if "%1"=="install-service" (
    call :install_service
) else if "%1"=="uninstall-service" (
    call :uninstall_service
) else (
    echo Usage: %0 {deploy^|start^|stop^|restart^|status^|install-service^|uninstall-service}
    echo.
    echo Commands:
    echo   deploy            - Full deployment with code update and worker restart
    echo   start             - Start queue workers
    echo   stop              - Stop queue workers
    echo   restart           - Restart queue workers
    echo   status            - Show worker status
    echo   install-service   - Install as Windows service ^(requires NSSM^)
    echo   uninstall-service - Uninstall Windows service
    exit /b 1
)

exit /b 0
