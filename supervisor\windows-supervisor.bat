@echo off
REM Windows batch script to manage Laravel queue workers
REM This script provides basic supervisor functionality for Windows environments

set PROJECT_PATH=c:\xampp\htdocs\ORACLE
set PHP_PATH=c:\xampp\php\php.exe
set LOG_PATH=%PROJECT_PATH%\storage\logs

REM Create logs directory if it doesn't exist
if not exist "%LOG_PATH%" mkdir "%LOG_PATH%"

:start_workers
echo Starting Laravel Queue Workers...

REM Start multiple worker processes
start "Laravel Worker 1" /min cmd /c "%PHP_PATH% %PROJECT_PATH%\artisan queue:work --sleep=3 --tries=3 --max-time=3600 >> %LOG_PATH%\worker1.log 2>&1"
start "Laravel Worker 2" /min cmd /c "%PHP_PATH% %PROJECT_PATH%\artisan queue:work --sleep=3 --tries=3 --max-time=3600 >> %LOG_PATH%\worker2.log 2>&1"

echo Queue workers started. Check logs in %LOG_PATH%
echo Press Ctrl+C to stop monitoring, or close this window to keep workers running.

REM Monitor workers (optional)
:monitor
timeout /t 30 /nobreak > nul
tasklist /fi "windowtitle eq Laravel Worker*" | find "cmd.exe" > nul
if errorlevel 1 (
    echo Workers stopped. Restarting...
    goto start_workers
)
goto monitor
